# Microchip AI Chatbot - VS Code Extension

A VS Code extension that provides an AI-powered chatbot interface for Microchip development assistance. This extension integrates with the Microchip AI API to help developers with questions about microcontrollers, development tools, and products.

## Features

- 🤖 **AI-Powered Assistance**: Direct integration with Microchip's AI API
- 🔐 **Secure API Key Storage**: API keys are stored securely in VS Code's secret storage
- 💬 **Chat Interface**: Clean, intuitive chat interface in a VS Code webview panel
- 🔄 **Persistent Sessions**: Chat history and API key are preserved across VS Code sessions
- 📱 **Sidebar Integration**: Quick access via sidebar icon in the Explorer panel
- 🌐 **Backend Proxy**: Includes Express.js server to handle CORS and API requests

## Installation

### Option 1: Install from VSIX (Recommended)

1. Download the `microchip-ai-chatbot-1.0.0.vsix` file
2. Open VS Code
3. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) to open the command palette
4. Type "Extensions: Install from VSIX..." and select it
5. Browse to and select the downloaded `.vsix` file
6. Restart VS Code when prompted

### Option 2: Development Installation

1. Clone this repository
2. Open the project in VS Code
3. Run `npm install` to install dependencies
4. Press `F5` to launch a new Extension Development Host window
5. The extension will be loaded in the new window

## Setup

### 1. Start the Backend Server

The extension requires a backend server to proxy requests to the Microchip AI API (due to CORS restrictions).

```bash
# In the project directory
npm run server
```

The server will start on `http://localhost:3001`

### 2. Get Your Microchip AI API Key

1. Visit the Microchip AI platform
2. Sign up or log in to your account
3. Generate an API key from your dashboard
4. Copy the API key for use in the extension

### 3. Configure the Extension

1. Click the Microchip AI Chatbot icon in the VS Code sidebar (Explorer panel)
2. Enter your API key when prompted
3. Click "Connect" to test the connection
4. Start chatting with the AI assistant!

## Usage

### Opening the Chat

- **Sidebar**: Click the Microchip AI Chatbot icon in the Explorer sidebar
- **Command Palette**: Press `Ctrl+Shift+P` and search for "Open Microchip AI Chat"
- **Panel**: The chat opens in a webview panel that can be moved and resized

### Chat Features

- **Send Messages**: Type your questions about Microchip products and development
- **Clear History**: Use the "Clear" button to reset the conversation
- **Change API Key**: Use the "Change Key" button to update your API key
- **Persistent Storage**: Your API key and preferences are saved automatically

### Example Questions

- "How do I configure the ADC on a PIC32 microcontroller?"
- "What's the difference between PIC and AVR microcontrollers?"
- "Help me with I2C communication setup"
- "Recommend a microcontroller for my IoT project"

## Commands

The extension provides the following VS Code commands:

- `microchipAIChatbot.openChat`: Open the chat panel
- `microchipAIChatbot.resetApiKey`: Reset the stored API key

## Troubleshooting

### Common Issues

1. **"Network error" when sending messages**
   - Ensure the backend server is running on port 3001
   - Check that your API key is valid
   - Verify internet connectivity

2. **Extension not appearing in sidebar**
   - Restart VS Code after installation
   - Check that the extension is enabled in the Extensions panel

3. **API key not saving**
   - Ensure VS Code has permission to access secure storage
   - Try resetting the API key and entering it again

4. **Chat interface not loading**
   - Check the VS Code Developer Console for errors
   - Ensure the webview build completed successfully

## Development

### Building the Extension

```bash
# Install dependencies
npm install

# Build extension
npm run compile

# Build webview
npm run build:webview

# Build everything
npm run build:all

# Package extension
npm run package
```

### Project Structure

```
├── src/
│   ├── extension.ts              # Main extension entry point
│   ├── webview/                  # React components for webview
│   │   ├── App.tsx              # Main React app
│   │   ├── ApiKeyInput.tsx      # API key input component
│   │   ├── Chatbot.tsx          # Chat interface component
│   │   └── ChatbotWebviewProvider.ts # VS Code webview provider
│   └── services/
│       └── MicrochipAPI.ts      # API service class
├── server/
│   └── index.cjs                # Express.js proxy server
├── dist/                        # Built React app
├── out/                         # Compiled TypeScript
└── assets/                      # Extension assets
```

## Version 1.0.0

- Initial release
- Basic chat functionality with Microchip AI API
- Secure API key storage
- VS Code sidebar integration
- Backend proxy server for CORS handling
