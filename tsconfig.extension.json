{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "exclude": ["node_modules", ".vscode-test", "dist", "src/components", "src/main.tsx", "src/App.tsx", "src/App.css", "src/index.css", "src/webview"], "include": ["src/extension.ts", "src/webview/ChatbotWebviewProvider.ts"]}